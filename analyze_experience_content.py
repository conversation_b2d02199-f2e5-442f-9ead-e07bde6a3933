#!/usr/bin/env python3
"""
详细分析经验存储的内容和数据来源
"""

import sys
import os
import numpy as np
import torch

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from env.ieee30_env import IEEE30Env
from agents.gnn.gnn_ddpg import GNNDDPGAgent
from agents.gnn.graph_utils import state_to_graph
from config import ENV_CONFIG, AGENT_CONFIG

def analyze_experience_storage():
    """详细分析经验存储的内容和数据来源"""
    print("=" * 80)
    print("详细分析经验存储的内容和数据来源")
    print("=" * 80)
    
    # 创建环境和智能体
    env = IEEE30Env(
        file_path=ENV_CONFIG['file_path'],
        gen_schedule_file=ENV_CONFIG['gen_schedule_file'],
        debug=True,  # 启用调试输出
        enable_slack_bus=True,
        look_ahead=1
    )
    
    agent = GNNDDPGAgent(
        node_features=env.node_features,
        edge_features=env.edge_features,
        action_dim=env.action_space.shape[0],
        action_bound=1.0,
        time_steps=1
    )
    
    agent.env_graph_builder = env.graph_builder
    agent.set_env(env)
    
    # 重置环境并开始episode
    state = env.reset()
    agent.start_episode()
    
    print(f"Episode起始时刻: {env.episode_start_step}")
    print(f"平衡机索引: {env.slack_bus_id}")
    
    # 运行一个step进行详细分析
    print(f"\n{'='*60}")
    print("Step 1 - 详细分析经验存储内容")
    print(f"{'='*60}")
    
    current_time_idx = env.episode_start_step + env.time_step
    prev_time_idx = current_time_idx - 1
    
    print(f"当前时刻索引: {current_time_idx}")
    print(f"t-1时刻索引: {prev_time_idx}")
    
    # 1. 分析当前状态的构成
    print(f"\n--- 1. 当前状态分析 ---")
    print(f"状态向量长度: {len(state)}")
    print(f"状态向量: {state}")
    
    # 解析状态向量
    if len(state) >= 36:
        loads = state[:30]  # 负荷数据
        gens = state[30:36]  # 发电机数据
        wind = state[36:] if len(state) > 36 else []  # 风电数据
        
        print(f"负荷数据 (30个): {loads}")
        print(f"发电机数据 (6个): {gens}")
        if len(wind) > 0:
            print(f"风电数据: {wind}")
        
        # 分析平衡机数据来源
        slack_norm = gens[env.slack_bus_id]
        gen_min = env.gen_min[env.slack_bus_id]
        gen_max = env.gen_max[env.slack_bus_id]
        if gen_max > gen_min:
            slack_actual = (slack_norm * (gen_max - gen_min) + (gen_max + gen_min)) / 2
        else:
            slack_actual = gen_min
        
        print(f"平衡机归一化值: {slack_norm:.4f}")
        print(f"平衡机实际出力: {slack_actual:.2f}MW")
        
        # 验证数据来源
        csv_slack_t_minus_1 = env.gen_outputs_data.iloc[prev_time_idx][env.gen_cols[env.slack_bus_id]]
        print(f"CSV第{prev_time_idx}时刻平衡机出力: {csv_slack_t_minus_1:.2f}MW")
        print(f"状态中平衡机数据来源: {'✅ CSV(t-1)' if abs(slack_actual - csv_slack_t_minus_1) < 0.01 else '❌ 其他来源'}")
    
    # 2. 智能体选择动作
    print(f"\n--- 2. 智能体动作分析 ---")
    action = agent.select_action(state, env.graph_builder, add_noise=True)
    print(f"智能体原始动作: {action.detach().cpu().numpy()}")
    print(f"动作维度: {action.shape}")
    print(f"动作数据类型: {type(action)}")
    print(f"动作设备: {action.device}")
    
    # 3. 执行step
    print(f"\n--- 3. Step执行分析 ---")
    next_state, reward, done, info = env.step(action)
    
    print(f"奖励: {reward:.4f}")
    print(f"完成标志: {done}")
    print(f"信息字典键: {list(info.keys())}")
    
    # 分析调节后的动作
    adjusted_action = info.get('adjusted_action', action)
    if hasattr(adjusted_action, 'detach'):
        adjusted_action_np = adjusted_action.detach().cpu().numpy()
    else:
        adjusted_action_np = adjusted_action
    print(f"调节后动作: {adjusted_action_np}")
    print(f"调节后动作数据类型: {type(adjusted_action)}")
    
    # 4. 分析下一状态
    print(f"\n--- 4. 下一状态分析 ---")
    print(f"下一状态向量长度: {len(next_state)}")
    
    if len(next_state) >= 36:
        next_loads = next_state[:30]
        next_gens = next_state[30:36]
        next_wind = next_state[36:] if len(next_state) > 36 else []
        
        # 分析下一状态中的平衡机
        next_slack_norm = next_gens[env.slack_bus_id]
        if gen_max > gen_min:
            next_slack_actual = (next_slack_norm * (gen_max - gen_min) + (gen_max + gen_min)) / 2
        else:
            next_slack_actual = gen_min
        
        print(f"下一状态平衡机归一化值: {next_slack_norm:.4f}")
        print(f"下一状态平衡机实际出力: {next_slack_actual:.2f}MW")
        
        # 验证下一状态数据来源
        csv_slack_t = env.gen_outputs_data.iloc[current_time_idx][env.gen_cols[env.slack_bus_id]]
        print(f"CSV第{current_time_idx}时刻平衡机出力: {csv_slack_t:.2f}MW")
        print(f"下一状态平衡机数据来源: {'✅ CSV(t)' if abs(next_slack_actual - csv_slack_t) < 0.01 else '❌ 其他来源'}")
    
    # 5. 转换为图数据
    print(f"\n--- 5. 图数据转换分析 ---")
    expected_node_feature_dim = state.shape[0] // agent.time_steps
    graph_list = state_to_graph(
        state, 
        env.graph_builder, 
        time_steps=agent.time_steps,
        node_feature_dim=expected_node_feature_dim
    )
    
    next_graph_list = state_to_graph(
        next_state, 
        env.graph_builder, 
        time_steps=agent.time_steps,
        node_feature_dim=expected_node_feature_dim
    ) if not done else None
    
    print(f"状态图数量: {len(graph_list)}")
    print(f"状态图节点特征形状: {graph_list[0].x.shape}")
    print(f"状态图边特征形状: {graph_list[0].edge_attr.shape}")
    print(f"下一状态图数量: {len(next_graph_list) if next_graph_list else 0}")
    
    # 6. 存储经验
    print(f"\n--- 6. 经验存储详细分析 ---")
    original_reward, normalized_reward = agent.store_transition(
        graph_list, adjusted_action, reward, next_graph_list if not done else None, done, info
    )
    
    print(f"原始奖励: {original_reward:.4f}")
    print(f"归一化奖励: {normalized_reward:.4f}")
    
    # 7. 检查存储的经验内容
    print(f"\n--- 7. 存储经验内容详细检查 ---")
    if len(agent.replay_buffer.current_episode) > 0:
        transition = agent.replay_buffer.current_episode[-1]
        
        print(f"存储的经验包含以下键: {list(transition.keys())}")
        
        # 详细分析每个键的内容
        for key, value in transition.items():
            print(f"\n{key}:")
            if key == 'state_graphs':
                print(f"  类型: {type(value)}")
                print(f"  图数量: {len(value)}")
                if len(value) > 0:
                    print(f"  第一个图节点特征形状: {value[0].x.shape}")
                    print(f"  第一个图边特征形状: {value[0].edge_attr.shape}")
                    
                    # 检查图中的平衡机数据
                    # 假设平衡机连接到第1个节点（索引0）
                    node_features = value[0].x[0]  # 第一个节点的特征
                    print(f"  第一个节点特征: {node_features}")
                    
            elif key == 'next_state_graphs':
                if value is not None:
                    print(f"  类型: {type(value)}")
                    print(f"  图数量: {len(value)}")
                else:
                    print(f"  值: None (episode结束)")
                    
            elif key in ['action', 'original_action']:
                if hasattr(value, 'shape'):
                    print(f"  类型: {type(value)}")
                    print(f"  形状: {value.shape}")
                    print(f"  设备: {value.device}")
                    print(f"  值: {value.detach().cpu().numpy()}")
                else:
                    print(f"  类型: {type(value)}")
                    print(f"  值: {value}")
                    
            elif key in ['reward', 'original_reward']:
                if hasattr(value, 'item'):
                    print(f"  类型: {type(value)}")
                    print(f"  值: {value.item():.4f}")
                else:
                    print(f"  类型: {type(value)}")
                    print(f"  值: {value:.4f}")
                    
            elif key == 'done':
                print(f"  类型: {type(value)}")
                print(f"  值: {value}")
                
            elif key == 'reward_components':
                print(f"  类型: {type(value)}")
                if isinstance(value, dict):
                    for comp_key, comp_value in value.items():
                        if hasattr(comp_value, 'item'):
                            print(f"    {comp_key}: {comp_value.item():.4f}")
                        else:
                            print(f"    {comp_key}: {comp_value:.4f}")
                            
            elif key == 'system_state':
                print(f"  类型: {type(value)}")
                if isinstance(value, dict):
                    for sys_key, sys_value in value.items():
                        if isinstance(sys_value, list):
                            print(f"    {sys_key}: 列表长度 {len(sys_value)}")
                        else:
                            print(f"    {sys_key}: {sys_value}")
            else:
                print(f"  类型: {type(value)}")
                print(f"  值: {str(value)[:100]}...")  # 截断长输出
    
    print(f"\n{'='*80}")
    print("经验存储内容分析完成")
    print(f"{'='*80}")

if __name__ == "__main__":
    analyze_experience_storage()
