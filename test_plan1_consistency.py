#!/usr/bin/env python3
"""
测试方案1的完全一致性：验证状态构建和step执行都使用CSV的t-1时刻数据
"""

import sys
import os
import numpy as np

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from env.ieee30_env import IEEE30Env
from config import ENV_CONFIG

def test_plan1_consistency():
    """测试方案1的完全一致性"""
    print("=" * 80)
    print("测试方案1：完全一致的CSV基准方法")
    print("验证状态构建和step执行都使用CSV的t-1时刻数据")
    print("=" * 80)
    
    # 创建环境
    env = IEEE30Env(
        file_path=ENV_CONFIG['file_path'],
        gen_schedule_file=ENV_CONFIG['gen_schedule_file'],
        debug=True,
        enable_slack_bus=True,
        look_ahead=1
    )
    
    # 重置环境
    state = env.reset()
    print(f"Episode起始时刻: {env.episode_start_step}")
    print(f"平衡机索引: {env.slack_bus_id}")
    
    # 运行5个步骤，验证完全一致性
    for step in range(5):
        print(f"\n{'='*70}")
        print(f"Step {step + 1} - 完全一致性验证")
        print(f"{'='*70}")
        
        current_time_idx = env.episode_start_step + env.time_step
        prev_time_idx = (current_time_idx - 1) % len(env.gen_outputs_data)
        
        print(f"当前时刻索引: {current_time_idx}")
        print(f"t-1时刻索引: {prev_time_idx}")
        
        # 1. 从状态中提取平衡机出力
        state_slack_output = None
        if len(state) >= 36:
            state_gen_norm = state[30 + env.slack_bus_id]
            gen_min = env.gen_min[env.slack_bus_id]
            gen_max = env.gen_max[env.slack_bus_id]
            if gen_max > gen_min:
                state_slack_output = (state_gen_norm * (gen_max - gen_min) + (gen_max + gen_min)) / 2
            else:
                state_slack_output = gen_min
            print(f"状态中平衡机出力: {state_slack_output:.2f}MW")
        
        # 2. 从CSV读取t-1时刻的平衡机出力
        csv_slack_t_minus_1 = env.gen_outputs_data.iloc[prev_time_idx][env.gen_cols[env.slack_bus_id]]
        print(f"CSV第{prev_time_idx}时刻(t-1)平衡机出力: {csv_slack_t_minus_1:.2f}MW")
        
        # 3. 验证状态与CSV的一致性
        if state_slack_output is not None:
            diff = abs(state_slack_output - csv_slack_t_minus_1)
            is_consistent = diff < 0.01
            print(f"状态与CSV(t-1)一致性: {'✅' if is_consistent else '❌'} (差异: {diff:.4f}MW)")
            
            if not is_consistent:
                print(f"⚠️ 发现不一致！状态: {state_slack_output:.2f}MW, CSV: {csv_slack_t_minus_1:.2f}MW")
        
        # 4. 执行step
        action_dim = len(env.controlled_gen_indices)
        if env.wind_power_enabled:
            action_dim += 1
        action = np.random.uniform(-0.1, 0.1, action_dim)
        print(f"执行动作: {action}")
        
        next_state, reward, done, info = env.step(action)
        
        # 5. 验证step执行时的CSV基准
        if hasattr(env.dc_pf, 'csv_slack_output'):
            step_csv_base = env.dc_pf.csv_slack_output
            print(f"Step执行时CSV基准: {step_csv_base:.2f}MW")
            
            # 验证step执行时的基准与状态中的值是否一致
            if state_slack_output is not None:
                step_diff = abs(state_slack_output - step_csv_base)
                step_consistent = step_diff < 0.01
                print(f"状态与Step基准一致性: {'✅' if step_consistent else '❌'} (差异: {step_diff:.4f}MW)")
                
                if not step_consistent:
                    print(f"⚠️ Step基准不一致！状态: {state_slack_output:.2f}MW, Step基准: {step_csv_base:.2f}MW")
        
        # 6. 显示调节结果
        final_slack = env.ppc['gen'][env.slack_bus_id, 1]
        print(f"调节后平衡机出力: {final_slack:.2f}MW")
        
        # 7. 验证奖励和其他信息
        print(f"奖励: {reward:.4f}")
        if 'power_imbalance' in info:
            print(f"功率不平衡: {info['power_imbalance']:.4f}MW")
        
        # 8. 总结本步的一致性
        all_consistent = True
        if state_slack_output is not None:
            state_csv_consistent = abs(state_slack_output - csv_slack_t_minus_1) < 0.01
            if hasattr(env.dc_pf, 'csv_slack_output'):
                state_step_consistent = abs(state_slack_output - env.dc_pf.csv_slack_output) < 0.01
                all_consistent = state_csv_consistent and state_step_consistent
            else:
                all_consistent = state_csv_consistent
        
        print(f"本步一致性总结: {'✅ 完全一致' if all_consistent else '❌ 存在不一致'}")
        
        state = next_state
        
        if done:
            print("Episode提前结束")
            break
    
    print(f"\n{'='*80}")
    print("方案1一致性测试完成")
    print("如果所有步骤都显示'✅ 完全一致'，则方案1实现成功")
    print(f"{'='*80}")

def test_multiple_episodes():
    """测试多个Episode的一致性"""
    print(f"\n{'='*80}")
    print("测试多个Episode的一致性")
    print(f"{'='*80}")
    
    env = IEEE30Env(
        file_path=ENV_CONFIG['file_path'],
        gen_schedule_file=ENV_CONFIG['gen_schedule_file'],
        debug=False,  # 关闭详细输出
        enable_slack_bus=True,
        look_ahead=1
    )
    
    inconsistent_count = 0
    total_steps = 0
    
    for episode in range(3):
        print(f"\nEpisode {episode + 1}:")
        state = env.reset()
        print(f"  起始时刻: {env.episode_start_step}")
        
        for step in range(3):
            # 检查状态一致性
            if len(state) >= 36:
                state_gen_norm = state[30 + env.slack_bus_id]
                gen_min = env.gen_min[env.slack_bus_id]
                gen_max = env.gen_max[env.slack_bus_id]
                if gen_max > gen_min:
                    state_slack_output = (state_gen_norm * (gen_max - gen_min) + (gen_max + gen_min)) / 2
                else:
                    state_slack_output = gen_min
                
                # 获取对应的CSV数据
                current_time_idx = env.episode_start_step + env.time_step
                prev_time_idx = (current_time_idx - 1) % len(env.gen_outputs_data)
                csv_slack = env.gen_outputs_data.iloc[prev_time_idx][env.gen_cols[env.slack_bus_id]]
                
                diff = abs(state_slack_output - csv_slack)
                is_consistent = diff < 0.01
                
                if not is_consistent:
                    inconsistent_count += 1
                    print(f"    Step {step + 1}: ❌ 不一致 (差异: {diff:.4f}MW)")
                else:
                    print(f"    Step {step + 1}: ✅ 一致")
                
                total_steps += 1
            
            # 执行step
            action_dim = len(env.controlled_gen_indices)
            if env.wind_power_enabled:
                action_dim += 1
            action = np.random.uniform(-0.05, 0.05, action_dim)
            state, reward, done, info = env.step(action)
            
            if done:
                break
    
    consistency_rate = (total_steps - inconsistent_count) / total_steps * 100
    print(f"\n一致性统计:")
    print(f"  总步数: {total_steps}")
    print(f"  不一致步数: {inconsistent_count}")
    print(f"  一致性率: {consistency_rate:.1f}%")
    
    if consistency_rate >= 99:
        print("✅ 方案1实现成功！一致性率达到99%以上")
    else:
        print("❌ 方案1仍需改进，一致性率不足99%")

if __name__ == "__main__":
    # 详细测试
    test_plan1_consistency()
    
    # 统计测试
    test_multiple_episodes()
