#!/usr/bin/env python3
"""
测试运行一个完整Episode，检查平衡机CSV读取修改是否存在问题
"""

import sys
import os
import numpy as np

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from env.ieee30_env import IEEE30Env
from config import ENV_CONFIG

def test_episode_run():
    """测试运行一个完整Episode"""
    print("=" * 80)
    print("测试运行一个完整Episode")
    print("=" * 80)
    
    try:
        # 创建环境
        print("1. 创建环境...")
        env = IEEE30Env(
            file_path=ENV_CONFIG['file_path'],
            gen_schedule_file=ENV_CONFIG['gen_schedule_file'],
            debug=True,  # 启用调试输出
            enable_slack_bus=True,
            look_ahead=1
        )
        print("✅ 环境创建成功")
        
        # 重置环境
        print("\n2. 重置环境...")
        state = env.reset()
        print(f"✅ 环境重置成功，起始时刻: {env.episode_start_step}")
        print(f"状态形状: {np.array(state).shape if hasattr(state, 'shape') else 'N/A'}")
        
        # 获取动作维度
        action_dim = len(env.controlled_gen_indices)
        if env.wind_power_enabled:
            action_dim += 1
        print(f"动作维度: {action_dim}")
        print(f"受控发电机索引: {env.controlled_gen_indices}")
        print(f"平衡机索引: {env.slack_bus_id}")
        
        # 运行几个时间步
        print("\n3. 运行Episode步骤...")
        max_steps = 5  # 只运行5步进行测试
        
        for step in range(max_steps):
            print(f"\n--- Step {step + 1} ---")
            
            # 生成随机动作
            action = np.random.uniform(-0.2, 0.2, action_dim)
            print(f"动作: {action}")
            
            # 执行step
            try:
                next_state, reward, done, info = env.step(action)
                
                print(f"✅ Step执行成功")
                print(f"奖励: {reward:.4f}")
                print(f"完成: {done}")
                
                # 检查关键信息
                if 'adjusted_action' in info:
                    adj_action = info['adjusted_action']
                    if hasattr(adj_action, 'detach'):
                        adj_action = adj_action.detach().cpu().numpy()
                    print(f"调节后动作: {adj_action}")
                
                # 检查平衡机信息
                slack_output = env.ppc['gen'][env.slack_bus_id, 1]
                print(f"平衡机当前出力: {slack_output:.2f}MW")
                
                # 检查CSV基准是否设置
                if hasattr(env.dc_pf, 'csv_slack_output'):
                    print(f"CSV基准出力: {env.dc_pf.csv_slack_output:.2f}MW")
                else:
                    print("⚠️ 未找到CSV基准出力")
                
                # 检查功率平衡
                if 'power_imbalance' in info:
                    print(f"功率不平衡: {info['power_imbalance']:.4f}MW")
                
                # 检查约束违反
                if 'line_violation' in info:
                    print(f"线路违反: {info['line_violation']:.4f}MW")
                
                if done:
                    print("Episode提前结束")
                    break
                    
            except Exception as e:
                print(f"❌ Step {step + 1} 执行失败: {str(e)}")
                import traceback
                traceback.print_exc()
                break
        
        print(f"\n✅ Episode测试完成，共执行 {step + 1} 步")
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False
    
    print("\n" + "=" * 80)
    print("Episode测试结果: 成功")
    print("=" * 80)
    return True

def test_csv_data_access():
    """测试CSV数据访问"""
    print("\n" + "=" * 80)
    print("测试CSV数据访问")
    print("=" * 80)
    
    try:
        env = IEEE30Env(
            file_path=ENV_CONFIG['file_path'],
            gen_schedule_file=ENV_CONFIG['gen_schedule_file'],
            debug=False,
            enable_slack_bus=True
        )
        
        if env.gen_outputs_data is not None:
            print(f"✅ CSV数据加载成功")
            print(f"数据形状: {env.gen_outputs_data.shape}")
            print(f"平衡机列: {env.gen_cols[env.slack_bus_id]}")
            
            # 测试几个时刻的数据访问
            test_indices = [0, 100, 1000, 5000]
            for idx in test_indices:
                if idx < len(env.gen_outputs_data):
                    slack_output = env.gen_outputs_data.iloc[idx][env.gen_cols[env.slack_bus_id]]
                    print(f"时刻 {idx}: 平衡机出力 = {slack_output:.2f}MW")
                else:
                    print(f"时刻 {idx}: 超出数据范围")
        else:
            print("❌ CSV数据未加载")
            
    except Exception as e:
        print(f"❌ CSV数据访问测试失败: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    # 首先测试CSV数据访问
    test_csv_data_access()
    
    # 然后测试Episode运行
    success = test_episode_run()
    
    if not success:
        print("\n⚠️ 发现问题，请检查修改的代码")
    else:
        print("\n✅ 所有测试通过，修改看起来正常")
