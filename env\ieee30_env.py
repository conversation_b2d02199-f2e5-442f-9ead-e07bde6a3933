import numpy as np
import pandas as pd
import sys
import os
import scipy.sparse as sp
import numpy.linalg as la
from pypower.api import makeB
from pypower.case30 import case30
from pypower.idx_bus import BUS_I, PD
from pypower.idx_gen import GEN_BUS
from pypower.idx_brch import F_BUS, T_BUS

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.abspath(os.path.join(current_dir, '..'))
if project_root not in sys.path:
    sys.path.append(project_root)

from agents.gnn.graph_utils import IEEE30GraphBuilder
from env.dc_power_flow import DCPowerFlow

# 设置输出不缓冲
sys.stdout.reconfigure(line_buffering=True)  # Python 3.7+

class IEEE30Env:
    """
    - 状态维度根据实际系统状态确定
    - 动作 5 维 (不含平衡机，控制其余5台机组)
    在 step() 中执行单步调度
    """

    def __init__(
            self,
            file_path="data/active_load_profile.csv",
            successful_timepoints_file="data/successful_timepoints.csv",  # 添加成功时间点文件参数
            gen_outputs_file="data/power_schedule_with_wind_sequential.csv",  # 修改默认的发电机出力文件
            gen_schedule_file=None,  # 新增：发电机调度文件参数
            use_successful_timepoints=None,  # 从config中获取，不设默认值
            look_ahead=1,  # 修改为单步调度
            enable_slack_bus=True,  # 是否启用平衡机
            wind_power_enabled=False,  # 风电功能开关
            **kwargs):
        self.ppc = case30()
        self.num_gens = len(self.ppc["gen"])
        # 临时先设为全部控制，后续根据是否启用平衡机调整
        self.num_controlled_gens = self.num_gens

        # 风电功能配置（需要在使用前先设置）
        self.wind_power_enabled = wind_power_enabled
        self.wind_power_max = 50.0  # 风电最大容量50MW
        self.wind_power_bus = 6  # 风电接入节点7（0-indexed为6）

        # 添加节点和边特征维度
        self.node_features = 3  # 节点特征：负荷、是否有发电机、发电机出力
        self.edge_features = 1  # 边特征：支路潮流

        # 添加动作空间
        class ActionSpace:
            def __init__(self, shape):
                self.shape = shape
        # 动作空间：发电机控制 + 风电控制（如果启用）
        action_dim = self.num_controlled_gens + (1 if self.wind_power_enabled else 0)
        self.action_space = ActionSpace([action_dim])

        # 添加图构建器
        self.graph_builder = IEEE30GraphBuilder(
            num_buses=30,
            num_branches=41,
            num_gens=6
        )

        # 初始化自定义DC潮流计算器
        self.dc_pf = DCPowerFlow(
            verbose=kwargs.get('debug', False),
            wind_power_bus=self.wind_power_bus if self.wind_power_enabled else None
        )
        
        # 在加载case数据前，先对发电机成本系数进行调整
        gencost = self.ppc['gencost'].copy()
        
        self.dc_pf.load_case(
            buses=self.ppc['bus'],
            branches=self.ppc['branch'],
            generators=self.ppc['gen'],
            gencost=gencost  # 使用修改后的成本系数
        )
        
        # 配置平衡机
        self.enable_slack_bus = enable_slack_bus
        
        # 启用或禁用平衡机功能
        if not self.enable_slack_bus:
            self.dc_pf.set_slack_generator(None)  # 禁用平衡机
            if kwargs.get('debug', False):
                print("已禁用平衡机功能", flush=True)
        else:
            # 自动选择容量最大的发电机作为平衡机
            self.dc_pf.find_largest_generator()
            # 获取选择的平衡机索引
            self.slack_bus_id = self.dc_pf.slack_gen_idx
            if kwargs.get('debug', False):
                print(f"已配置平衡机: 发电机 {self.slack_bus_id + 1}", flush=True)
        
        # 确保环境中的成本系数与DC潮流计算器一致
        self.ppc['gencost'] = gencost
        
        # 构建系统矩阵
        self.dc_pf.build_matrices()

        # 加载负荷数据
        self.load_data = pd.read_csv(file_path)
        self.total_time_points = len(self.load_data)
        
        # 加载发电机出力数据（包含最优发电成本和风电）
        # 优先使用gen_schedule_file，如果没有则使用gen_outputs_file
        schedule_file = gen_schedule_file if gen_schedule_file is not None else gen_outputs_file
        try:
            full_gen_data = pd.read_csv(schedule_file)
            print(f"已加载计划发电调度数据，包含{len(full_gen_data)}个时间点")

            # 第一列是时间点，第二列是风电，第3-8列是发电机G1-G6，第9列是PeriodCost_Dollar
            self.scheduled_wind_data = full_gen_data.iloc[:, 1]  # 计划上网的风电

            # 保存完整的CSV数据，包含所有列（发电机出力 + 成本数据）
            gen_columns = ['G1', 'G2', 'G3', 'G4', 'G5', 'G6']
            if all(col in full_gen_data.columns for col in gen_columns):
                # 保存完整数据，包含发电机出力和成本信息
                self.gen_outputs_data = full_gen_data  # 包含所有列的完整数据
                self.gen_cols = gen_columns[:self.num_gens]  # 确保长度匹配
                if len(self.gen_cols) != self.num_gens:
                    raise ValueError(f"CSV中检测到的机组列 {self.gen_cols} 数量与系统机组数 {self.num_gens} 不匹配")
                print(f"检测到机组列: {self.gen_cols}")

                # 验证PeriodCost_Dollar列是否存在
                if 'PeriodCost_Dollar' in full_gen_data.columns:
                    print(f"检测到成本列: PeriodCost_Dollar")
                else:
                    print(f"警告: 未找到PeriodCost_Dollar列，可用列: {full_gen_data.columns.tolist()}")
            else:
                raise ValueError(f"CSV文件中缺少必要的发电机列: {gen_columns}")
        except Exception as e:
            print(f"加载发电机出力数据失败: {e}")
            self.gen_outputs_data = None
            self.scheduled_wind_data = None
        
        # 若未成功解析机组列，则回退为默认顺序索引
        if self.gen_outputs_data is None:
            self.gen_cols = None
        
        # 加载成功时间点数据
        self.use_successful_timepoints = use_successful_timepoints

        # 风电相关变量初始化
        self.current_wind_available = 0.0  # 当前可用风电容量
        self.last_wind_output = 0.0  # 上一时刻风电实际出力

        # 加载风电数据（如果启用）
        self.wind_data = None
        if self.wind_power_enabled and hasattr(self, 'gen_outputs_data') and self.gen_outputs_data is not None:
            try:
                # 从CSV文件第2列读取风电数据
                if 'WindPower_MW' in full_gen_data.columns:
                    self.wind_data = full_gen_data['WindPower_MW']
                    print(f"已加载风电数据，包含{len(self.wind_data)}个时间点")
                else:
                    print("警告：CSV文件中未找到WindPower_MW列")
            except Exception as e:
                print(f"加载风电数据失败: {e}")

        self.max_time = 96  # 修改为一天96个时间步（每15分钟一个时间步）
        self.look_ahead = look_ahead  # 单步调度
        self.time_step = 0
        self.episode_start_step = 0  # 用于记录一个 episode 的起始时刻
        self.current_start_cost = None  # 用于记录当前episode起始点对应的最优发电成本

        # 发电机上下限直接从 case30 提取
        self.gen_min = self.ppc['gen'][:, 9].tolist()  # Pmin
        self.gen_max = self.ppc['gen'][:, 8].tolist()  # Pmax

        # 负荷上下限
        self.load_min = 0.0
        self.load_max = [18.07485, 35.9386, 14.06605, 25.73675, 25.35845,
                         20.18005, 22.90845, 22.6828, 25.73675, 26.07005,
                         18.8197, 16.3983, 13.04705, 22.5782, 15.51475,
                         25.35845, 29.9193, 17.9497, 8.70225, 19.414,
                         11.3105, 13.8214, 22.02105, 24.04785, 23.2504,
                         18.92515, 20.108, 8.26125, 18.448, 25.73675]
        
        self.state = None

        # 发电机成本函数系数
        self.gen_cost_a = self.ppc['gencost'][:, 4].tolist()
        self.gen_cost_b = self.ppc['gencost'][:, 5].tolist()
        self.gen_cost_c = self.ppc['gencost'][:, 6].tolist()

        # 发电机差异化爬坡限制（与OPF测试保持一致）
        self.ramp_rates = [0.25, 0.25, 0.30, 0.30, 0.35, 0.35]  # 提高爬坡率
        self.gen_ramp_up = []
        self.gen_ramp_down = []
        for i in range(len(self.gen_max)):
            ramp_limit = self.gen_max[i] * self.ramp_rates[i]
            self.gen_ramp_up.append(ramp_limit)
            self.gen_ramp_down.append(ramp_limit)

        # 线路潮流限制（使用 rateA）
        self.line_flow_limit = self.ppc['branch'][:, 5].tolist()

        # 设置约束、惩罚权重和其他参数
        self.gen_cost_weight = kwargs.get('gen_cost_weight', -1.0)        # 发电成本权重
        self.power_balance_weight = kwargs.get('power_balance_weight', -100.0)  # 功率平衡惩罚权重
        self.voltage_weight = kwargs.get('voltage_weight', -10.0)   # 电压约束惩罚权重
        self.w1 = kwargs.get('ramp_violation_penalty', -15.0)        # 爬坡限制惩罚权重
        self.gen_limit_weight = kwargs.get('gen_limit_penalty', -10.0)    # 发电机越限惩罚权重
        self.w2 = kwargs.get('branch_violation_penalty', -15.0)      # 线路负载约束惩罚权重

        # 风电相关惩罚权重（仅在启用风电时使用）
        if self.wind_power_enabled:
            self.wind_limit_penalty_weight = kwargs.get('wind_limit_penalty', -20.0)  # 风电出力限制惩罚权重
            self.spinning_reserve_penalty_weight = kwargs.get('spinning_reserve_penalty', -25.0)  # 旋转备用惩罚权重
            self.wind_ramp_penalty_weight = kwargs.get('wind_ramp_penalty', -15.0)  # 风电爬坡惩罚权重

        # 添加运行时统计
        self.running_cost_mean = 0.0
        self.running_cost_std = 1.0
        self.running_cost_count = 0

        # 添加debug标志
        self.debug = kwargs.get('debug', False)

        # --------- 设置受智能体控制的机组列表 ---------
        if self.enable_slack_bus:
            # 受控机组 = 所有非平衡机
            self.controlled_gen_indices = [i for i in range(self.num_gens) if i != self.slack_bus_id]
        else:
            self.controlled_gen_indices = list(range(self.num_gens))

        self.num_controlled_gens = len(self.controlled_gen_indices)
        # 重新设置动作空间形状（包含风电控制）
        action_dim = self.num_controlled_gens + (1 if self.wind_power_enabled else 0)
        self.action_space.shape = [action_dim]

    def _build_ptdf_matrix(self):
        """构建PTDF（功率传输分布因子）矩阵，使用完整的直流潮流模型"""
        print("开始构建完整PTDF矩阵...", flush=True)
        
        # 1. 创建电力系统数据的深拷贝
        ppc = self.ppc.copy()
        nb = ppc['bus'].shape[0]  # 节点数量
        nl = ppc['branch'].shape[0]  # 线路数量
        
        # 2. 确保母线编号从0开始连续
        # 先获取原始的母线编号
        original_bus_numbers = ppc['bus'][:, BUS_I].astype(int)
        # 创建映射字典 (从0开始)
        self.old_to_new_bus_map = {old: new for new, old in enumerate(original_bus_numbers)}
        self.new_to_old_bus_map = {new: old for old, new in self.old_to_new_bus_map.items()}
        
        print("母线映射关系:", flush=True)
        for old, new in self.old_to_new_bus_map.items():
            print(f"原始母线 {old} -> 新母线 {new}", flush=True)
        
        # 3. 更新母线编号
        for i in range(nb):
            old_number = int(ppc['bus'][i, BUS_I])
            ppc['bus'][i, BUS_I] = self.old_to_new_bus_map[old_number]
        
        # 4. 更新支路的起始和终止母线编号
        for i in range(nl):
            old_from = int(ppc['branch'][i, F_BUS])
            old_to = int(ppc['branch'][i, T_BUS])
            ppc['branch'][i, F_BUS] = self.old_to_new_bus_map[old_from]
            ppc['branch'][i, T_BUS] = self.old_to_new_bus_map[old_to]
        
        # 5. 更新发电机连接的母线编号
        for i in range(len(ppc['gen'])):
            old_bus = int(ppc['gen'][i, GEN_BUS])
            ppc['gen'][i, GEN_BUS] = self.old_to_new_bus_map[old_bus]
        
        # 6. 选择参考节点（使用第一个发电机所连接的节点）
        ref_bus = int(ppc['gen'][0, GEN_BUS])
        print(f"参考节点编号: {ref_bus}", flush=True)
        
        # 7. 构建节点导纳矩阵
        B, Bf = makeB(ppc['baseMVA'], ppc['bus'], ppc['branch'], 2)
        
        # 8. 删除参考节点对应的行和列
        mask = np.ones(nb, dtype=bool)
        mask[ref_bus] = False  # 使用0基索引
        B = B[mask][:, mask]
        
        # 9. 计算PTDF矩阵
        # 首先计算除参考节点外的节点的相角
        X = sp.linalg.inv(B.tocsc())
        
        # 创建完整的PTDF矩阵
        self.ptdf = np.zeros((nl, nb))
        
        # 对每条线路计算PTDF
        for i in range(nl):
            f_bus = int(ppc['branch'][i, F_BUS])  # 已经是0基索引
            t_bus = int(ppc['branch'][i, T_BUS])  # 已经是0基索引
            
            # 获取线路参数
            x = ppc['branch'][i, 3]  # 电抗
            if abs(x) < 1e-6:
                x = 1e-6
            
            # 计算PTDF系数
            if f_bus != ref_bus and t_bus != ref_bus:
                # 两端都不是参考节点
                f_idx = f_bus if f_bus < ref_bus else f_bus-1
                t_idx = t_bus if t_bus < ref_bus else t_bus-1
                for j in range(nb):
                    if j != ref_bus:
                        j_idx = j if j < ref_bus else j-1
                        self.ptdf[i, j] = (1/x) * (X[f_idx, j_idx] - X[t_idx, j_idx])
            elif f_bus == ref_bus:
                # 起始端是参考节点
                t_idx = t_bus if t_bus < ref_bus else t_bus-1
                for j in range(nb):
                    if j != ref_bus:
                        j_idx = j if j < ref_bus else j-1
                        self.ptdf[i, j] = -(1/x) * X[t_idx, j_idx]
            elif t_bus == ref_bus:
                # 终止端是参考节点
                f_idx = f_bus if f_bus < ref_bus else f_bus-1
                for j in range(nb):
                    if j != ref_bus:
                        j_idx = j if j < ref_bus else j-1
                        self.ptdf[i, j] = (1/x) * X[f_idx, j_idx]
        
        # 10. 创建发电机节点映射
        self.gen_bus_map = {}
        for i in range(self.num_gens):
            bus_idx = int(ppc['gen'][i, GEN_BUS])  # 已经是0基索引
            if bus_idx not in self.gen_bus_map:
                self.gen_bus_map[bus_idx] = []
            self.gen_bus_map[bus_idx].append(i)
        
        # 11. 设置风电母线索引
        if hasattr(self, 'wind_power_bus'):
            self.wind_bus_idx_zero_based = self.wind_power_bus
        else:
            self.wind_bus_idx_zero_based = 2
        
        print(f"PTDF矩阵构建成功，尺寸: {self.ptdf.shape}", flush=True)

    def reset(self, random_start=True):
        """
        重置环境到初始状态，随机选择起始负荷时间点
        
        Returns:
            初始状态
        """
        # 随机选择起始负荷时间点
        self.episode_start_step = np.random.randint(0, self.total_time_points - self.max_time - 1)
        self.current_start_cost = None

        # 初始化时间步
        self.time_step = 0
        
        # 更新初始负荷数据
        for i in range(len(self.ppc['bus'])):
            self.ppc['bus'][i, PD] = self.load_data.iloc[self.episode_start_step, i]
        
        # 确保DC潮流计算器的负荷数据也被更新
        self.dc_pf.loads = self.ppc['bus'][:, PD].copy()
        
        # 智能初始化发电机出力：优先使用历史数据，只在必要时优化
        if self.episode_start_step == 0:
            # 第0时刻：没有上一时刻数据，需要优化初始调度
            if self.debug:
                print(f"Episode开始于第0时刻，使用优化的初始调度")

            # 先设置为最小值
            for i in range(self.num_gens):
                self.ppc['gen'][i, 1] = self.ppc['gen'][i, 9]  # 设置为Pmin

            # 优化初始发电机调度
            verbose_orig = self.dc_pf.verbose
            if self.debug:
                self.dc_pf.verbose = True
            self.dc_pf.optimize_initial_dispatch()
            self.dc_pf.verbose = verbose_orig

            # 从DC潮流计算器同步优化后的发电机出力到环境
            for i in range(self.num_gens):
                self.ppc['gen'][i, 1] = self.dc_pf.generators[i][1]
        else:
            # 非第0时刻：直接使用CSV文件中上一时刻的真实出力值
            prev_time_step = self.episode_start_step - 1
            if self.gen_outputs_data is not None and prev_time_step < len(self.gen_outputs_data):
                # 从完整数据中提取发电机出力列
                prev_gen_output = self.gen_outputs_data.iloc[prev_time_step][self.gen_cols].values
                for i in range(self.num_gens):
                    self.ppc['gen'][i, 1] = prev_gen_output[i]

                # 同步发电机出力数据到DC潮流计算器
                for i in range(self.num_gens):
                    self.dc_pf.generators[i][1] = self.ppc['gen'][i, 1]

                if self.debug:
                    print(f"Episode开始于第{self.episode_start_step}时刻，使用第{prev_time_step}时刻的真实出力: {prev_gen_output}")
            else:
                # 回退到优化方案（如果CSV数据不可用）
                if self.debug:
                    print(f"警告：无法获取第{prev_time_step}时刻的真实出力，使用优化的初始调度")
                for i in range(self.num_gens):
                    self.ppc['gen'][i, 1] = self.ppc['gen'][i, 9]  # 设置为Pmin

                verbose_orig = self.dc_pf.verbose
                if self.debug:
                    self.dc_pf.verbose = True
                self.dc_pf.optimize_initial_dispatch()
                self.dc_pf.verbose = verbose_orig

                for i in range(self.num_gens):
                    self.ppc['gen'][i, 1] = self.dc_pf.generators[i][1]

                # 确保数据一致性（从DC潮流计算器同步到环境后，数据已经一致）

        # 初始化上一时刻发电机出力（用于爬坡限制计算）
        self.last_gen_output = self.ppc['gen'][:, 1].copy()

        # 获取初始状态
        self.state = self._get_state()

        return self.state

    def _get_state(self):
        """
        构建实时状态 (单步状态而非多步前瞻)。
        如果启用风力发电，会包含风力发电预测信息
        """
        # 当前调度时刻 t 对应的索引（负荷）
        load_idx = (self.episode_start_step + self.time_step) % self.total_time_points  # t
        prev_idx = (self.episode_start_step + self.time_step - 1) % self.total_time_points  # t-1

        # ------------------ 读取负荷 csv(t) ------------------
        load_30 = self.load_data.iloc[load_idx].values
        
        # 对负荷进行归一化（可以根据历史数据的最大最小值进行归一化）
        load_max = np.max(self.load_data.values, axis=0)  # 计算每列（每个节点）的最大值
        load_min = np.min(self.load_data.values, axis=0)  # 计算每列（每个节点）的最小值
        load_30_norm = []
        
        for i, load in enumerate(load_30):
            # 使用历史数据的范围进行归一化到[-1,1]
            if load_max[i] > load_min[i]:
                norm_load = (2 * load - (load_max[i] + load_min[i])) / (load_max[i] - load_min[i])
            else:
                norm_load = 0  # 如果最大值等于最小值，设为0避免除零错误
            load_30_norm.append(norm_load)

        # ------------------ 读取机组出力 csv(t-1) ------------------
        gen_6 = None
        if hasattr(self, 'gen_outputs_data') and self.gen_outputs_data is not None:
            try:
                if self.gen_cols is not None:
                    csv_row = self.gen_outputs_data.loc[prev_idx, self.gen_cols].astype(float).values
                else:
                    csv_row = self.gen_outputs_data.iloc[prev_idx, :self.num_gens].astype(float).values
                if len(csv_row) == self.num_gens:
                    gen_6 = csv_row.tolist()
            except Exception:
                gen_6 = None

        # 若无法读取csv，则退回ppc中的实时值
        if gen_6 is None:
            gen_6 = [self.ppc['gen'][i, 1] for i in range(self.num_gens)]
        
        gen_6_norm = []
        for i, g in enumerate(gen_6):
            # 使用每个发电机特定的上下限进行归一化到[-1,1]范围
            if self.gen_max[i] > self.gen_min[i]:
                norm_g = ( 2 * g - (self.gen_max[i] + self.gen_min[i]) )/ (self.gen_max[i] - self.gen_min[i])
            else:
                norm_g = 0
            gen_6_norm.append(norm_g)
            
        # 添加风电状态信息（如果启用）
        if self.wind_power_enabled:
            # 获取当前可用风电容量并归一化
            wind_available_norm = 0.0
            if self.wind_data is not None:
                try:
                    wind_available = self.wind_data.iloc[load_idx]
                    # 使用风电最大容量进行归一化 [-1, 1]
                    wind_available_norm = (2 * wind_available - self.wind_power_max) / self.wind_power_max
                    # 更新当前可用风电容量
                    self.current_wind_available = wind_available
                except (IndexError, KeyError):
                    wind_available_norm = 0.0
                    self.current_wind_available = 0.0

            step_vec = np.concatenate([load_30_norm, gen_6_norm, [wind_available_norm]])
        else:
            step_vec = np.concatenate([load_30_norm, gen_6_norm])

        return step_vec.astype(np.float32)  # 直接返回当前时间步的状态向量

    def _normalize_penalty(self, value, max_value):
        """归一化惩罚值到[0,1]范围，同时动态更新最大值"""
        if abs(value) > max_value:
            max_value = abs(value)
        return min(abs(value) / max_value, 1.0), max_value

    def _update_cost_statistics(self, cost):
        """更新发电成本统计"""
        self.running_cost_count += 1
        delta = cost - self.running_cost_mean
        self.running_cost_mean += delta / self.running_cost_count
        if self.running_cost_count > 1:
            self.running_cost_std = ((self.running_cost_std ** 2 * (self.running_cost_count - 2) +
                                    delta * (cost - self.running_cost_mean)) /
                                   (self.running_cost_count - 1)) ** 0.5

    def differentiable_adjust_action(self, action, prev_gen_output=None, load_demand=None):
        """
        可微分的动作调节函数，用于训练时保持梯度传播

        Args:
            action: Actor原始输出 [6] 或 [num_controlled_gens] 范围[-1,1]
            prev_gen_output: 上一时刻发电机出力 (可选)
            load_demand: 负荷需求 (可选)

        Returns:
            adjusted_action: 调节后的动作，保留梯度，维度与输入一致
        """
        import torch

        # 确保输入是tensor并在正确设备上
        if not isinstance(action, torch.Tensor):
            action = torch.tensor(action, dtype=torch.float32, requires_grad=True)

        device = action.device
        num_controlled_gens = len(self.controlled_gen_indices)

        # 🔧 方案1：分离发电机动作和风电动作
        if len(action) > num_controlled_gens:
            # 输入包含风电动作
            gen_actions = action[:num_controlled_gens]  # 发电机动作 [5]
            wind_actions = action[num_controlled_gens:]  # 风电动作 [1]
            has_wind = True
        else:
            # 输入只有发电机动作
            gen_actions = action  # [5]
            wind_actions = None
            has_wind = False

        # 获取上一时刻发电机出力
        if prev_gen_output is None:
            prev_gen_output = torch.tensor([self.ppc['gen'][i, 1] for i in self.controlled_gen_indices],
                                         dtype=torch.float32, device=device)
        elif not isinstance(prev_gen_output, torch.Tensor):
            prev_gen_output = torch.tensor(prev_gen_output, dtype=torch.float32, device=device)

        # 获取负荷需求
        if load_demand is None:
            load_demand = torch.tensor(self.ppc['bus'][:, 2].sum(), dtype=torch.float32, device=device)
        elif not isinstance(load_demand, torch.Tensor):
            load_demand = torch.tensor(load_demand, dtype=torch.float32, device=device)

        # 1. 只对发电机动作进行可微分调节
        physical_gen_actions = self._differentiable_action_mapping(gen_actions, prev_gen_output, device)

        # 2. 可微分的爬坡限制
        ramp_limited_gen_actions = self._differentiable_ramp_limiting(physical_gen_actions, prev_gen_output, device)

        # 3. 可微分的功率平衡
        balanced_gen_actions = self._differentiable_power_balancing(ramp_limited_gen_actions, load_demand, device)

        # 4. 🔧 重新组合：保持原始维度
        if has_wind:
            # 重新组合发电机动作和风电动作
            full_adjusted_actions = torch.cat([balanced_gen_actions, wind_actions])
            return full_adjusted_actions
        else:
            # 只返回发电机动作
            return balanced_gen_actions

    def _differentiable_action_mapping(self, gen_actions, prev_gen_output, device):
        """可微分的发电机动作映射到物理范围"""
        import torch

        gen_min = torch.tensor([self.gen_min[i] for i in self.controlled_gen_indices],
                              dtype=torch.float32, device=device)
        gen_max = torch.tensor([self.gen_max[i] for i in self.controlled_gen_indices],
                              dtype=torch.float32, device=device)

        # 线性映射：[-1,1] -> [gen_min, gen_max]
        # 但考虑当前出力作为基准
        # 🔧 修复：避免inplace操作，使用列表收集结果
        physical_actions_list = []

        # 🔧 现在gen_actions已经是纯发电机动作，维度匹配
        for i in range(len(gen_actions)):
            if gen_actions[i] >= 0:
                # 正向调整：从当前出力到最大出力
                margin = gen_max[i] - prev_gen_output[i]
                adjustment = margin * gen_actions[i]
            else:
                # 负向调整：从当前出力到最小出力
                margin = prev_gen_output[i] - gen_min[i]
                adjustment = margin * gen_actions[i]  # gen_actions[i]是负数

            # 🔧 修复：不使用inplace操作
            physical_actions_list.append(prev_gen_output[i] + adjustment)

        # 🔧 修复：从列表创建新的tensor，避免inplace操作
        physical_actions = torch.stack(physical_actions_list)

        # 软裁剪到物理限制
        physical_actions = self._soft_clamp(physical_actions, gen_min, gen_max)

        return physical_actions

    def _differentiable_ramp_limiting(self, actions, prev_gen_output, device):
        """可微分的爬坡限制"""
        import torch

        ramp_limits = torch.tensor([self.ramp_rates[i] * self.gen_max[i] for i in self.controlled_gen_indices],
                                  dtype=torch.float32, device=device)

        # 计算调整量
        adjustments = actions - prev_gen_output

        # 软爬坡限制：使用tanh函数平滑裁剪
        scale = 0.1  # 控制软硬程度，越小越接近硬约束
        soft_limited_adjustments = torch.tanh(adjustments / (ramp_limits * scale + 1e-8)) * ramp_limits

        return prev_gen_output + soft_limited_adjustments

    def _differentiable_power_balancing(self, actions, load_demand, device):
        """可微分的功率平衡"""
        import torch

        # 计算功率不平衡
        total_gen = actions.sum()
        imbalance = total_gen - load_demand

        # 按发电机容量比例分配不平衡量
        gen_capacities = torch.tensor([self.gen_max[i] for i in self.controlled_gen_indices],
                                     dtype=torch.float32, device=device)
        capacity_ratios = gen_capacities / (gen_capacities.sum() + 1e-8)

        # 分配调整量
        adjustments = imbalance * capacity_ratios
        balanced_actions = actions - adjustments

        # 软裁剪到物理限制
        gen_min = torch.tensor([self.gen_min[i] for i in self.controlled_gen_indices],
                              dtype=torch.float32, device=device)
        gen_max = torch.tensor([self.gen_max[i] for i in self.controlled_gen_indices],
                              dtype=torch.float32, device=device)

        final_actions = self._soft_clamp(balanced_actions, gen_min, gen_max)

        return final_actions

    def _soft_clamp(self, x, min_val, max_val):
        """软裁剪函数，使用sigmoid实现可微分的裁剪"""
        import torch

        # 归一化到[0,1]
        normalized = (x - min_val) / (max_val - min_val + 1e-8)

        # 使用sigmoid进行软裁剪，steepness控制软硬程度
        steepness = 6.0  # 越大越接近硬裁剪
        soft_clipped = torch.sigmoid(steepness * (normalized - 0.5))

        # 映射回原始范围
        return min_val + soft_clipped * (max_val - min_val)

    def step(self, action):
        """
        执行一步动作，返回下一个状态、奖励和是否结束
        
        Args:
            action: 动作向量 [6] - 对应6个发电机的出力调整比例
                    每个值范围[-1,1]，表示在当前出力基础上调整的比例
                    -1表示调整到最小出力，1表示调整到最大出力
                    
        Returns:
            next_state: 下一个状态
            reward: 奖励
            done: 是否结束
            info: 其他信息，包括各种惩罚项和电力系统状态
        """
        # ============ 使用CSV真实出力作为上一时刻基准 ============
        # 计算当前与上一物理时间点索引（环绕）
        csv_curr_idx = (self.episode_start_step + self.time_step) % self.total_time_points
        csv_prev_idx = (self.episode_start_step + self.time_step - 1) % self.total_time_points

        csv_curr_row = None
        csv_prev_row = None
        if hasattr(self, 'gen_outputs_data') and self.gen_outputs_data is not None:
            try:
                if self.gen_cols is not None:
                    csv_curr_row = self.gen_outputs_data.loc[csv_curr_idx, self.gen_cols].astype(float).values
                    csv_prev_row = self.gen_outputs_data.loc[csv_prev_idx, self.gen_cols].astype(float).values
                else:
                    csv_curr_row = self.gen_outputs_data.iloc[csv_curr_idx, :self.num_gens].astype(float).values
                    csv_prev_row = self.gen_outputs_data.iloc[csv_prev_idx, :self.num_gens].astype(float).values
            except Exception:
                pass

        # ---- 1) 用上一时刻实际出力作为环境初始机组出力（prev_gen_output) ----
        # 优先使用环境保存的上一时刻实际出力，其次使用CSV数据
        if hasattr(self, 'last_gen_output') and self.last_gen_output is not None:
            # 使用上一时刻的实际发电机出力
            for gi in range(self.num_gens):
                self.ppc['gen'][gi, 1] = self.last_gen_output[gi]
            prev_gen_output = self.last_gen_output.copy()

            # 同步发电机出力数据到DC潮流计算器，确保DC潮流计算器使用正确的起始点
            for gi in range(self.num_gens):
                self.dc_pf.generators[gi][1] = self.last_gen_output[gi]
        elif csv_prev_row is not None and len(csv_prev_row) == self.num_gens:
            # 回退到CSV数据
            for gi in range(self.num_gens):
                self.ppc['gen'][gi, 1] = csv_prev_row[gi]
            prev_gen_output = csv_prev_row.copy()

            # 同步CSV数据到DC潮流计算器
            for gi in range(self.num_gens):
                self.dc_pf.generators[gi][1] = csv_prev_row[gi]
        else:
            # 最后回退到当前ppc中的值（应该是上一时刻的结果）
            prev_gen_output = self.ppc['gen'][:, 1].copy()

            # 同步当前ppc数据到DC潮流计算器
            for gi in range(self.num_gens):
                self.dc_pf.generators[gi][1] = self.ppc['gen'][gi, 1]
        # csv_curr_row 保留用于监督损失，不写入 ppc
        # ===========================================================
        
        # 初始化关键变量
        power_flow_success = True
        self.adjusted_action = None  # 初始化调节后的动作
        branch_violations = []
        gen_violations = 0
        final_imbalance = 0.0
        initial_imbalance = 0.0
        power_balance = None
        adjustment_results = None
        gen_cost = 0.0  # 初始化发电成本
        branch_flows = None
        gen_penalty = 0.0

        # 如果潮流计算失败，使用一个较大的惩罚值
        MAX_PENALTY = 1e6

        # 1. 从action更新发电机出力
        # 始终保持PyTorch张量格式以保留梯度
        import torch

        if not isinstance(action, torch.Tensor):
            # 如果不是tensor，转换为tensor
            action = torch.tensor(action, dtype=torch.float32, requires_grad=True)

        # 确保动作在[-1,1]范围内，使用PyTorch操作
        action = torch.clamp(action, -1, 1)

        # 分离发电机动作和风电动作（始终使用PyTorch操作）
        gen_action = action[:self.num_controlled_gens]
        wind_action = 0.0
        if self.wind_power_enabled and len(action) > self.num_controlled_gens:
            wind_action = action[self.num_controlled_gens]  # 风电动作 [-1, 1]
        
        # 更新所有发电机出力
        controlled_gen_idx = 0  # 仅计数非平衡机动作
        gen_violated_ramp = []

        for i in range(self.num_gens):
            is_slack = self.enable_slack_bus and (i == self.slack_bus_id)

            if is_slack:
                # 跳过智能体控制，保持ppc中的当前值，后续平衡机机制会调整
                continue

            # 防御：确保动作索引不越界
            if controlled_gen_idx >= len(gen_action):
                raise ValueError(f"动作向量长度 {len(gen_action)} 不匹配受控机组数量 {self.num_controlled_gens}.")

            act_val = gen_action[controlled_gen_idx]

            # 根据动作值将出力线性映射到 [Pmin, Pmax]
            if act_val >= 0:
                margin = self.gen_max[i] - prev_gen_output[i]
                adjustment = margin * act_val
            else:
                margin = prev_gen_output[i] - self.gen_min[i]
                adjustment = margin * act_val

            # 检查爬坡限制
            ramp_limit = self.ramp_rates[i] * self.gen_max[i]

            # 处理PyTorch张量和numpy的兼容性
            if isinstance(adjustment, torch.Tensor):
                adjustment_abs = torch.abs(adjustment)
                if adjustment_abs > ramp_limit:
                    adjustment = torch.sign(adjustment) * ramp_limit
                    gen_violated_ramp.append(i)
                # 转换为numpy用于更新ppc
                adjustment_val = adjustment.detach().cpu().numpy().item()
            else:
                if abs(adjustment) > ramp_limit:
                    adjustment = np.sign(adjustment) * ramp_limit
                    gen_violated_ramp.append(i)
                adjustment_val = adjustment

            # 更新出力并裁剪在物理范围内
            new_pg = prev_gen_output[i] + adjustment_val
            new_pg = max(self.gen_min[i], min(self.gen_max[i], new_pg))
            self.ppc['gen'][i, 1] = new_pg

            controlled_gen_idx += 1
        
        # 为方便调试，输出超出爬坡限制的发电机 (仅在数量较多时输出)
        if self.debug and len(gen_violated_ramp) > 2:
            print(f"以下发电机超出爬坡限制，已自动限制调整量: {gen_violated_ramp}", flush=True)
        
        # 2. 获取当前状态（将在return时作为state_t）
        current_state = self._get_state()
        
        # 3. 计算奖励和其他信息
        for t in range(self.look_ahead):
            # 更新负荷
            time_idx = self.episode_start_step + self.time_step + t
            if time_idx >= self.total_time_points:
                time_idx = time_idx % self.total_time_points
            
            for i in range(len(self.ppc['bus'])):
                # 直接从CSV文件读取实际负荷值，而不是计算比例
                self.ppc['bus'][i, PD] = self.load_data.iloc[time_idx, i]
            
            # 更新DC潮流计算器的负荷数据
            self.dc_pf.loads = self.ppc['bus'][:, PD].copy()

            # 处理风电出力（如果启用）
            current_wind_output = 0.0
            if self.wind_power_enabled:
                # 将风电动作 [-1, 1] 转换为实际出力 [0, current_wind_available]
                if self.current_wind_available > 0:
                    # wind_action [-1, 1] -> [0, 1] -> [0, current_wind_available]
                    wind_utilization = (wind_action + 1) / 2  # 转换到 [0, 1]
                    current_wind_output = wind_utilization * self.current_wind_available
                    # 确保不超过风电最大容量
                    current_wind_output = min(current_wind_output, self.wind_power_max)

                # 设置风电出力到DC潮流计算器
                self.dc_pf.set_wind_power(current_wind_output)

            # 设置发电机出力
            self.dc_pf.set_gen_dispatch(self.ppc['gen'][:, 1].tolist())

            # 从CSV读取当前时刻的平衡机出力作为调节基准
            if self.enable_slack_bus and self.gen_outputs_data is not None:
                current_time_idx = self.episode_start_step + self.time_step + t
                if current_time_idx < len(self.gen_outputs_data):
                    # 读取CSV中当前时刻的平衡机出力
                    csv_slack_output = self.gen_outputs_data.iloc[current_time_idx][self.gen_cols[self.slack_bus_id]]
                    # 设置到DC潮流计算器中，供平衡机调节使用
                    self.dc_pf.csv_slack_output = csv_slack_output
                    if self.debug and t == 0:
                        print(f"从CSV读取第{current_time_idx}时刻平衡机出力: {csv_slack_output:.2f}MW")
                else:
                    # 如果超出CSV范围，使用循环索引
                    wrapped_idx = current_time_idx % len(self.gen_outputs_data)
                    csv_slack_output = self.gen_outputs_data.iloc[wrapped_idx][self.gen_cols[self.slack_bus_id]]
                    self.dc_pf.csv_slack_output = csv_slack_output
                    if self.debug and t == 0:
                        print(f"CSV数据循环，使用第{wrapped_idx}时刻平衡机出力: {csv_slack_output:.2f}MW")

            # 仅当功率不平衡较大或特别请求时才开启详细输出
            should_log_detailed = self.debug and (t == 0)
            
            # 运行潮流计算，然后检查是否输出详细信息
            if should_log_detailed:
                verbose_orig = self.dc_pf.verbose
                self.dc_pf.verbose = True
                # 传递上一时刻的发电机出力，用于检查爬坡约束
                result = self.dc_pf.run_dc_power_flow(prev_outputs=prev_gen_output)
                self.dc_pf.verbose = verbose_orig
            else:
                # 运行DC潮流计算，传递上一时刻的发电机出力
                result = self.dc_pf.run_dc_power_flow(prev_outputs=prev_gen_output)
                
            if not result:
                power_flow_success = False
                if self.debug:
                    print(f"时间步 {self.time_step+t}: 潮流计算失败!", flush=True)
                break
            
            # 从DC潮流计算器更新发电机出力
            for i in range(self.num_gens):
                self.ppc['gen'][i, 1] = self.dc_pf.generators[i][1]
                
            # 记录调节后的动作（包含风电出力），保持张量格式
            import torch

            # 获取原始动作的设备信息
            device = action.device if isinstance(action, torch.Tensor) else torch.device('cpu')

            if self.wind_power_enabled:
                # 调节后动作 = [调节后的受控机组出力, 风电出力]
                gen_outputs = torch.tensor([self.ppc['gen'][i, 1] for i in self.controlled_gen_indices],
                                         dtype=torch.float32, device=device, requires_grad=True)
                wind_output = torch.tensor([current_wind_output],
                                         dtype=torch.float32, device=device, requires_grad=True)
                self.adjusted_action = torch.cat([gen_outputs, wind_output])
            else:
                # 只有发电机出力
                self.adjusted_action = torch.tensor([self.ppc['gen'][i, 1] for i in self.controlled_gen_indices],
                                                   dtype=torch.float32, device=device, requires_grad=True)
            

            
            # 获取潮流计算结果
            branch_flows = self.dc_pf.branch_flows
            power_balance = self.dc_pf.get_power_balance()
            branch_violations = self.dc_pf.check_branch_limits()
            
            # 获取平衡机调整结果
            adjustment_results = self.dc_pf.adjustment_results
            initial_imbalance = adjustment_results['initial_imbalance']
            final_imbalance = adjustment_results['final_imbalance']
            
            # 计算发电成本
            gen_cost = 0
            for i in range(self.num_gens):
                pg = self.ppc['gen'][i, 1]
                gen_cost += (self.gen_cost_a[i] * pg * pg + 
                           self.gen_cost_b[i] * pg + 
                           self.gen_cost_c[i])
            
            # 读取当前时刻的最优发电成本
            if time_idx >= self.total_time_points:
                time_idx = time_idx % self.total_time_points
            
            # 从CSV文件的PeriodCost_Dollar列读取最优发电成本（如果存在）
            optimal_cost = None
            if hasattr(self, 'gen_outputs_data') and self.gen_outputs_data is not None:
                try:
                    # 优先从PeriodCost_Dollar列读取
                    if 'PeriodCost_Dollar' in self.gen_outputs_data.columns:
                        optimal_cost = self.gen_outputs_data.loc[time_idx, 'PeriodCost_Dollar']
                    else:
                        # 回退到最后一列（向后兼容）
                        optimal_cost = self.gen_outputs_data.iloc[time_idx, -1]
                except (IndexError, AttributeError, KeyError):
                    optimal_cost = None
            
            # 计算各种惩罚项
            gen_penalty = 0
            
            # 分别计算发电机越限惩罚和爬坡限制惩罚
            gen_limit_penalty = 0  # 发电机越限惩罚
            ramp_penalty = 0  # 爬坡限制惩罚
            
            for i in range(self.num_gens):
                pg = self.ppc['gen'][i, 1]
                pg_min = self.gen_min[i]
                pg_max = self.gen_max[i]
                
                # 检查发电机是否在可行范围内（仅计算惩罚，不修改出力）
                if pg < pg_min - 1e-3:
                    gen_limit_penalty += (pg_min - pg)  # 添加越限惩罚
                    if should_log_detailed:
                        print(f"发电机{i+1}低于最小出力: {pg} < {pg_min}", flush=True)
                elif pg > pg_max + 1e-3:
                    gen_limit_penalty += (pg - pg_max)  # 添加越限惩罚
                    if should_log_detailed:
                        print(f"发电机{i+1}超过最大出力: {pg} > {pg_max}", flush=True)
                
                # 检查爬坡限制
                ramp_limit = self.ramp_rates[i] * self.gen_max[i]  # MW为单位
                delta_pg = abs(pg - prev_gen_output[i])
                if delta_pg > ramp_limit + 1e-3:
                    ramp_penalty += (delta_pg - ramp_limit)  # 添加爬坡惩罚
                    if self.debug and delta_pg > ramp_limit * 1.5:  # 仅在严重超出时输出
                        print(f"发电机{i+1}超出爬坡限制: {delta_pg} > {ramp_limit}", flush=True)
                        
            # 将爬坡惩罚赋值给gen_penalty（保持与现有代码兼容）
            gen_penalty = ramp_penalty

            # 计算风电相关惩罚（仅在启用风电时）
            wind_limit_penalty = 0.0
            spinning_reserve_penalty = 0.0
            wind_ramp_penalty = 0.0

            if self.wind_power_enabled:
                # 1. 风电出力限制惩罚：风电出力 ≤ 可用风电容量
                if current_wind_output > self.current_wind_available + 1e-3:
                    wind_limit_penalty = -(current_wind_output - self.current_wind_available)
                    if should_log_detailed:
                        print(f"风电出力超过可用容量: {current_wind_output:.2f} > {self.current_wind_available:.2f}", flush=True)

                # 2. 旋转备用约束惩罚：所有火电机组总可用上调容量 ≥ 风电预测值 × 10%
                total_upward_reserve = 0.0
                for i in range(self.num_gens):
                    pg = self.ppc['gen'][i, 1]
                    pg_max = self.gen_max[i]
                    total_upward_reserve += max(0, pg_max - pg)

                required_reserve = self.current_wind_available * 0.10  # 降低到10%的旋转备用
                if total_upward_reserve < required_reserve - 1e-3:
                    spinning_reserve_penalty = -(required_reserve - total_upward_reserve)
                    if should_log_detailed:
                        print(f"旋转备用不足: {total_upward_reserve:.2f} < {required_reserve:.2f}", flush=True)

                # 3. 风电向下爬坡约束惩罚：相邻时刻风电下降 ≤ 20MW（放宽限制）
                wind_ramp_down = self.last_wind_output - current_wind_output
                if wind_ramp_down > 20.0 + 1e-3:
                    wind_ramp_penalty = -(wind_ramp_down - 20.0)
                    if should_log_detailed:
                        print(f"风电向下爬坡过大: {wind_ramp_down:.2f} > 20.0", flush=True)

                # 更新上一时刻风电出力
                self.last_wind_output = current_wind_output

        # 根据潮流计算结果设置惩罚值
        if power_flow_success:
            # 潮流计算成功时的惩罚计算
            gen_cost_penalty = -gen_cost  # 发电成本惩罚（负值）
            power_balance_penalty = -abs(final_imbalance) * 10.0  # 功率平衡惩罚
            branch_penalty = 0.0
            if branch_violations and 'summary' in branch_violations:
                branch_penalty = -branch_violations['summary']['total_violation_mw']  # 线路过载惩罚
        else:
            # 潮流计算失败时的惩罚
            gen_cost_penalty = -MAX_PENALTY  # 最大惩罚
            power_balance_penalty = -MAX_PENALTY
            branch_penalty = -MAX_PENALTY
            gen_limit_penalty = -MAX_PENALTY
            gen_penalty = -MAX_PENALTY  # 爬坡限制惩罚
            # 风电相关惩罚也设为最大惩罚
            wind_limit_penalty = -MAX_PENALTY if self.wind_power_enabled else 0.0
            spinning_reserve_penalty = -MAX_PENALTY if self.wind_power_enabled else 0.0
            wind_ramp_penalty = -MAX_PENALTY if self.wind_power_enabled else 0.0
            if self.debug:
                print("潮流计算失败，使用最大惩罚值", flush=True)

        # 计算总奖励（包含风电相关惩罚）
        reward = (self.gen_cost_weight * gen_cost_penalty +          # 发电成本
                 self.power_balance_weight * power_balance_penalty + # 功率不平衡惩罚（包含风电）
                 self.w2 * branch_penalty +                          # 线路过载惩罚
                 self.w1 * gen_penalty +                             # 爬坡限制惩罚
                 self.gen_limit_weight * gen_limit_penalty)          # 发电机越限惩罚

        # 添加风电相关惩罚（仅在启用风电时）
        if self.wind_power_enabled:
            reward += (self.wind_limit_penalty_weight * wind_limit_penalty +      # 风电出力限制惩罚
                      self.spinning_reserve_penalty_weight * spinning_reserve_penalty +  # 旋转备用惩罚
                      self.wind_ramp_penalty_weight * wind_ramp_penalty)          # 风电爬坡惩罚

        # 获取平衡机信息
        slack_gen_info = None
        if self.enable_slack_bus:
            idx = self.slack_bus_id
            slack_gen_info = {
                'output': self.ppc['gen'][idx, 1],
                'min': self.gen_min[idx],
                'max': self.gen_max[idx],
                'margin_up': self.gen_max[idx] - self.ppc['gen'][idx, 1],
                'margin_down': self.ppc['gen'][idx, 1] - self.gen_min[idx],
                'capacity': self.gen_max[idx]
            }
            
        # 如果adjusted_action为空（潮流计算失败），构造包含风电的动作
        if self.adjusted_action is None:
            if self.wind_power_enabled:
                gen_outputs = np.array([self.ppc['gen'][i, 1] for i in self.controlled_gen_indices])
                self.adjusted_action = np.concatenate([gen_outputs, [current_wind_output]])
            else:
                self.adjusted_action = np.array([self.ppc['gen'][i, 1] for i in self.controlled_gen_indices])

        # 保存关键信息到info字典
        info = {
            'gen_cost': gen_cost,
            'gen_cost_reward': gen_cost_penalty,
            'power_imbalance': final_imbalance,
            'segment_imbalance': power_balance['segment_imbalance'] if power_balance else MAX_PENALTY,  # 添加时间段不平衡
            'power_balance_reward': power_balance_penalty,
            'line_violation': branch_violations['summary']['total_violation_mw'] if power_flow_success else MAX_PENALTY,
            'branch_violation_reward': branch_penalty,
            'gen_violation': gen_penalty,
            'ramp_violation': 0.0,  # 暂时未使用
            'convergence_success': power_flow_success,
            'adjusted_action': self.adjusted_action,  # 添加调节后的实际发电机出力
            'reward_components': {
                'gen_cost': gen_cost_penalty,
                'power_balance': power_balance_penalty,  # 保留键名但不再计入总奖励
                'branch_violation': branch_penalty,
                'gen_violation': gen_penalty,  # 改为爬坡限制惩罚
                'gen_limit': gen_limit_penalty,  # 新增发电机越限惩罚
            },
            'system_state': {
                'gen_output': self.ppc['gen'][:, 1].tolist(),
                'load': self.ppc['bus'][:, PD].tolist(),
                'branch_flow': branch_flows.tolist() if branch_flows is not None else [],
                'branch_limit': self.line_flow_limit,
                'time_point': self.episode_start_step + self.time_step  # 添加当前时间点信息
            },
            'slack_info': slack_gen_info if self.enable_slack_bus else None,
            'optimal_cost': optimal_cost,
            'csv_curr_output': csv_curr_row.tolist() if csv_curr_row is not None else None
        }

        # 添加风电相关信息（仅在启用风电时）
        if self.wind_power_enabled:
            info['wind_info'] = {
                'wind_available': self.current_wind_available,
                'wind_output': current_wind_output,
                'wind_utilization': current_wind_output / max(self.current_wind_available, 1e-6),
                'wind_limit_penalty': wind_limit_penalty,
                'spinning_reserve_penalty': spinning_reserve_penalty,
                'wind_ramp_penalty': wind_ramp_penalty,
                'last_wind_output': self.last_wind_output
            }
            # 更新reward_components以包含风电惩罚
            info['reward_components'].update({
                'wind_limit': wind_limit_penalty,
                'spinning_reserve': spinning_reserve_penalty,
                'wind_ramp': wind_ramp_penalty
            })
        
        # -------- 重新构建 next_state (负荷 csv(t+1), 出力 P^t) --------
        next_load_idx = (self.episode_start_step + self.time_step + 1) % self.total_time_points  # t+1
        load_next = self.load_data.iloc[next_load_idx].values

        # 负荷归一化使用历史最大最小
        load_max = np.max(self.load_data.values, axis=0)
        load_min = np.min(self.load_data.values, axis=0)
        load_next_norm = []
        for i, l in enumerate(load_next):
            if load_max[i] > load_min[i]:
                norm_l = (2 * l - (load_max[i] + load_min[i])) / (load_max[i] - load_min[i])
            else:
                norm_l = 0
            load_next_norm.append(norm_l)

        # 发电机出力使用当前ppc中的结果（P^t）
        gen_next = [self.ppc['gen'][i, 1] for i in range(self.num_gens)]
        gen_next_norm = []
        for i, g in enumerate(gen_next):
            norm_g = (2 * g - (self.gen_max[i] + self.gen_min[i])) / (self.gen_max[i] - self.gen_min[i])
            gen_next_norm.append(norm_g)
        
        # 添加下一时刻的风电状态信息（如果启用）
        if self.wind_power_enabled:
            # 获取下一时刻的可用风电容量并归一化
            next_wind_available_norm = 0.0
            if self.wind_data is not None:
                try:
                    next_wind_available = self.wind_data.iloc[next_load_idx]
                    # 使用风电最大容量进行归一化 [-1, 1]
                    next_wind_available_norm = (2 * next_wind_available - self.wind_power_max) / self.wind_power_max
                except (IndexError, KeyError):
                    next_wind_available_norm = 0.0

            next_state = np.concatenate([load_next_norm, gen_next_norm, [next_wind_available_norm]]).astype(np.float32)
        else:
            next_state = np.concatenate([load_next_norm, gen_next_norm]).astype(np.float32)

        # ----------------------------------------------------------

        # 保存当前时刻的发电机出力，用于下一时刻的爬坡限制计算
        self.last_gen_output = self.ppc['gen'][:, 1].copy()

        self.time_step += 1
        done = (self.time_step >= self.max_time)

        return next_state, reward, done, info

