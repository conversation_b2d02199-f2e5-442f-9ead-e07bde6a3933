#!/usr/bin/env python3
"""
测试状态一致性：验证状态中的平衡机出力与step执行时的重置是否一致
"""

import sys
import os
import numpy as np

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from env.ieee30_env import IEEE30Env
from config import ENV_CONFIG

def test_state_consistency():
    """测试状态一致性"""
    print("=" * 80)
    print("测试状态一致性：验证状态中的平衡机出力与step执行时的重置是否一致")
    print("=" * 80)
    
    # 创建环境
    env = IEEE30Env(
        file_path=ENV_CONFIG['file_path'],
        gen_schedule_file=ENV_CONFIG['gen_schedule_file'],
        debug=True,
        enable_slack_bus=True,
        look_ahead=1
    )
    
    # 重置环境
    state = env.reset()
    print(f"Episode起始时刻: {env.episode_start_step}")
    print(f"平衡机索引: {env.slack_bus_id}")
    
    # 运行3个步骤，检查状态一致性
    for step in range(3):
        print(f"\n{'='*60}")
        print(f"Step {step + 1}")
        print(f"{'='*60}")
        
        current_time_idx = env.episode_start_step + env.time_step
        print(f"当前时刻索引: {current_time_idx}")
        
        # 1. 从状态中提取平衡机出力
        # 状态格式：[30个负荷, 6个发电机, 1个风电(如果启用)]
        state_slack_output = None
        if len(state) >= 36:
            # 发电机出力在状态向量的30-35位置，平衡机是第0个发电机
            state_gen_norm = state[30 + env.slack_bus_id]  # 归一化的平衡机出力
            
            # 反归一化得到实际出力
            gen_min = env.gen_min[env.slack_bus_id]
            gen_max = env.gen_max[env.slack_bus_id]
            if gen_max > gen_min:
                state_slack_output = (state_gen_norm * (gen_max - gen_min) + (gen_max + gen_min)) / 2
            else:
                state_slack_output = gen_min
                
            print(f"状态中平衡机出力: {state_slack_output:.2f}MW (归一化值: {state_gen_norm:.4f})")
        
        # 2. 从CSV读取对应时刻的平衡机出力
        if current_time_idx < len(env.gen_outputs_data):
            csv_slack = env.gen_outputs_data.iloc[current_time_idx][env.gen_cols[env.slack_bus_id]]
            print(f"CSV中第{current_time_idx}时刻平衡机出力: {csv_slack:.2f}MW")
            
            # 检查一致性
            if state_slack_output is not None:
                diff = abs(state_slack_output - csv_slack)
                is_consistent = diff < 0.01  # 允许0.01MW的误差
                print(f"状态与CSV一致性: {'✅' if is_consistent else '❌'} (差异: {diff:.4f}MW)")
        
        # 3. 执行step，观察平衡机重置
        action_dim = len(env.controlled_gen_indices)
        if env.wind_power_enabled:
            action_dim += 1
        action = np.random.uniform(-0.1, 0.1, action_dim)
        
        next_state, reward, done, info = env.step(action)
        
        # 4. 检查step执行时的CSV基准
        if hasattr(env.dc_pf, 'csv_slack_output'):
            step_csv_base = env.dc_pf.csv_slack_output
            print(f"Step执行时CSV基准: {step_csv_base:.2f}MW")
            
            # 检查step执行时的CSV基准与状态中的值是否一致
            if state_slack_output is not None:
                step_diff = abs(state_slack_output - step_csv_base)
                step_consistent = step_diff < 0.01
                print(f"状态与Step基准一致性: {'✅' if step_consistent else '❌'} (差异: {step_diff:.4f}MW)")
        
        # 5. 显示调节后的平衡机出力
        final_slack = env.ppc['gen'][env.slack_bus_id, 1]
        print(f"调节后平衡机出力: {final_slack:.2f}MW")
        
        state = next_state
        
        if done:
            break
    
    print(f"\n{'='*80}")
    print("测试完成")
    print(f"{'='*80}")

if __name__ == "__main__":
    test_state_consistency()
