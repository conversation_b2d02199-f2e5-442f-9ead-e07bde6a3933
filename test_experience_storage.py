#!/usr/bin/env python3
"""
测试经验存储的正确性
"""

import sys
import os
import numpy as np
import torch

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from env.ieee30_env import IEEE30Env
from agents.gnn.gnn_ddpg import GNNDDPGAgent
from agents.gnn.graph_utils import state_to_graph
from config import ENV_CONFIG, AGENT_CONFIG

def test_experience_storage():
    """测试经验存储的正确性"""
    print("=" * 80)
    print("测试经验存储的正确性")
    print("=" * 80)
    
    # 创建环境
    env = IEEE30Env(
        file_path=ENV_CONFIG['file_path'],
        gen_schedule_file=ENV_CONFIG['gen_schedule_file'],
        debug=False,
        enable_slack_bus=True,
        look_ahead=1
    )
    
    # 创建智能体
    agent = GNNDDPGAgent(
        node_features=env.node_features,
        edge_features=env.edge_features,
        action_dim=env.action_space.shape[0],
        action_bound=1.0,
        time_steps=1
    )
    
    # 设置环境引用
    agent.env_graph_builder = env.graph_builder
    agent.set_env(env)
    
    # 重置环境
    state = env.reset()
    agent.start_episode()
    
    print(f"Episode起始时刻: {env.episode_start_step}")
    print(f"平衡机索引: {env.slack_bus_id}")
    
    # 运行几个步骤，检查经验存储
    for step in range(3):
        print(f"\n{'='*60}")
        print(f"Step {step + 1} - 经验存储验证")
        print(f"{'='*60}")
        
        current_time_idx = env.episode_start_step + env.time_step
        prev_time_idx = current_time_idx - 1
        
        print(f"当前时刻索引: {current_time_idx}")
        print(f"t-1时刻索引: {prev_time_idx}")
        
        # 1. 检查当前状态中的平衡机出力
        if len(state) >= 36:
            state_gen_norm = state[30 + env.slack_bus_id]
            gen_min = env.gen_min[env.slack_bus_id]
            gen_max = env.gen_max[env.slack_bus_id]
            if gen_max > gen_min:
                state_slack_output = (state_gen_norm * (gen_max - gen_min) + (gen_max + gen_min)) / 2
            else:
                state_slack_output = gen_min
            print(f"当前状态中平衡机出力: {state_slack_output:.2f}MW")
        
        # 2. 从CSV读取对应的基准
        csv_slack_t_minus_1 = env.gen_outputs_data.iloc[prev_time_idx][env.gen_cols[env.slack_bus_id]]
        print(f"CSV第{prev_time_idx}时刻(t-1)平衡机出力: {csv_slack_t_minus_1:.2f}MW")
        
        # 3. 智能体选择动作
        action = agent.select_action(state, env.graph_builder, add_noise=True)
        print(f"智能体动作: {action.detach().cpu().numpy()}")
        
        # 4. 执行step
        next_state, reward, done, info = env.step(action)
        
        # 5. 检查调节后的动作
        adjusted_action = info.get('adjusted_action', action)
        if hasattr(adjusted_action, 'detach'):
            adjusted_action_np = adjusted_action.detach().cpu().numpy()
        else:
            adjusted_action_np = adjusted_action
        print(f"调节后动作: {adjusted_action_np}")
        
        # 6. 检查下一状态中的平衡机出力
        if len(next_state) >= 36:
            next_state_gen_norm = next_state[30 + env.slack_bus_id]
            if gen_max > gen_min:
                next_state_slack_output = (next_state_gen_norm * (gen_max - gen_min) + (gen_max + gen_min)) / 2
            else:
                next_state_slack_output = gen_min
            print(f"下一状态中平衡机出力: {next_state_slack_output:.2f}MW")
            
            # 检查下一状态应该对应的CSV时刻
            next_csv_idx = current_time_idx  # 下一状态应该对应当前时刻的CSV
            next_csv_slack = env.gen_outputs_data.iloc[next_csv_idx][env.gen_cols[env.slack_bus_id]]
            print(f"下一状态应对应CSV第{next_csv_idx}时刻: {next_csv_slack:.2f}MW")
            
            next_diff = abs(next_state_slack_output - next_csv_slack)
            print(f"下一状态一致性: {'✅' if next_diff < 0.01 else '❌'} (差异: {next_diff:.4f}MW)")
        
        # 7. 将状态转换为图数据
        expected_node_feature_dim = state.shape[0] // agent.time_steps
        graph_list = state_to_graph(
            state, 
            env.graph_builder, 
            time_steps=agent.time_steps,
            node_feature_dim=expected_node_feature_dim
        )
        
        next_graph_list = None
        if not done:
            next_graph_list = state_to_graph(
                next_state, 
                env.graph_builder, 
                time_steps=agent.time_steps,
                node_feature_dim=expected_node_feature_dim
            )
        
        # 8. 存储经验
        print(f"\n--- 存储经验 ---")
        print(f"状态图数量: {len(graph_list)}")
        print(f"下一状态图数量: {len(next_graph_list) if next_graph_list else 0}")
        print(f"奖励: {reward:.4f}")
        print(f"完成: {done}")
        
        # 存储transition
        original_reward, normalized_reward = agent.store_transition(
            graph_list, adjusted_action, reward, next_graph_list if not done else None, done, info
        )
        
        print(f"原始奖励: {original_reward:.4f}")
        print(f"归一化奖励: {normalized_reward:.4f}")
        
        # 9. 验证经验存储的一致性
        print(f"\n--- 经验存储一致性验证 ---")
        
        # 检查当前episode中的最后一个transition
        if len(agent.replay_buffer.current_episode) > 0:
            last_transition = agent.replay_buffer.current_episode[-1]
            
            # 检查存储的动作
            stored_action = last_transition['original_action']
            if hasattr(stored_action, 'detach'):
                stored_action_np = stored_action.detach().cpu().numpy()
            else:
                stored_action_np = stored_action
            print(f"存储的原始动作: {stored_action_np}")
            
            # 检查存储的奖励
            stored_reward = last_transition['original_reward']
            if hasattr(stored_reward, 'item'):
                stored_reward_val = stored_reward.item()
            else:
                stored_reward_val = stored_reward
            print(f"存储的原始奖励: {stored_reward_val:.4f}")
            
            # 验证动作一致性
            action_diff = np.abs(adjusted_action_np - stored_action_np).max()
            print(f"动作存储一致性: {'✅' if action_diff < 1e-6 else '❌'} (最大差异: {action_diff:.8f})")
            
            # 验证奖励一致性
            reward_diff = abs(reward - stored_reward_val)
            print(f"奖励存储一致性: {'✅' if reward_diff < 1e-6 else '❌'} (差异: {reward_diff:.8f})")
        
        # 10. 总结本步的经验存储
        print(f"\n本步经验存储总结:")
        print(f"  - 状态一致性: 基于CSV(t-1)的平衡机出力")
        print(f"  - 动作存储: 使用调节后的动作")
        print(f"  - 奖励存储: 原始奖励和归一化奖励")
        print(f"  - 下一状态: 基于CSV(t)的平衡机出力")
        
        state = next_state
        
        if done:
            break
    
    # 结束episode
    agent.end_episode()
    
    print(f"\n{'='*80}")
    print("经验存储测试完成")
    print(f"总经验数量: {len(agent.replay_buffer)}")
    print(f"{'='*80}")

def test_experience_replay():
    """测试经验回放的正确性"""
    print(f"\n{'='*80}")
    print("测试经验回放的正确性")
    print(f"{'='*80}")
    
    # 创建环境和智能体
    env = IEEE30Env(
        file_path=ENV_CONFIG['file_path'],
        gen_schedule_file=ENV_CONFIG['gen_schedule_file'],
        debug=False,
        enable_slack_bus=True,
        look_ahead=1
    )
    
    agent = GNNDDPGAgent(
        node_features=env.node_features,
        edge_features=env.edge_features,
        action_dim=env.action_space.shape[0],
        action_bound=1.0,
        time_steps=1
    )
    
    agent.env_graph_builder = env.graph_builder
    agent.set_env(env)
    
    # 收集一些经验
    for episode in range(2):
        state = env.reset()
        agent.start_episode()
        
        for step in range(5):
            action = agent.select_action(state, env.graph_builder, add_noise=True)
            next_state, reward, done, info = env.step(action)
            
            expected_node_feature_dim = state.shape[0] // agent.time_steps
            graph_list = state_to_graph(state, env.graph_builder, time_steps=agent.time_steps, node_feature_dim=expected_node_feature_dim)
            next_graph_list = state_to_graph(next_state, env.graph_builder, time_steps=agent.time_steps, node_feature_dim=expected_node_feature_dim) if not done else None
            
            adjusted_action = info.get('adjusted_action', action)
            agent.store_transition(graph_list, adjusted_action, reward, next_graph_list, done, info)
            
            state = next_state
            if done:
                break
        
        agent.end_episode()
    
    print(f"收集了 {len(agent.replay_buffer)} 个经验")
    
    # 测试经验回放
    if len(agent.replay_buffer) >= agent.batch_size:
        print(f"测试经验回放 (batch_size={agent.batch_size})")
        
        try:
            batch = agent.replay_buffer.sample(agent.batch_size)
            print(f"✅ 经验回放成功")
            print(f"批次包含的键: {list(batch.keys())}")
            
            if 'state_graphs' in batch:
                print(f"状态图批次大小: {len(batch['state_graphs'])}")
            if 'action' in batch:
                print(f"动作批次形状: {batch['action'].shape}")
            if 'reward' in batch:
                print(f"奖励批次形状: {batch['reward'].shape}")
                
        except Exception as e:
            print(f"❌ 经验回放失败: {str(e)}")
            import traceback
            traceback.print_exc()
    else:
        print(f"经验不足，无法进行回放测试 ({len(agent.replay_buffer)}/{agent.batch_size})")

if __name__ == "__main__":
    # 测试经验存储
    test_experience_storage()
    
    # 测试经验回放
    test_experience_replay()
